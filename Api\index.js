// import request from "./requests.js"
import request from "../config/request"
import axios from "axios";



export const uploadToOSS = async (file, onProgress) => {
	const formData = new FormData();
	formData.append('file', file);
	let token = uni.getStorageSync('welcome')
	console.log(token);
	return axios.post('/oss/api/resource/oss/upload', formData, {
			headers: {
					'Content-Type': 'multipart/form-data',
					'Authorization': `${token}` // 根据实际鉴权方式调整
			},
			onUploadProgress: (progressEvent) => {
					if (onProgress && progressEvent.total) {
							const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
							onProgress(percent);
					}
			}
	});
}

export function welcome(){
	return request.get("/api/funFunEsthetics/welcome")
}
// 调用AI
export function callAi(params){
	return request.post("/api/funFunEsthetics/invoke/AI",params)
}

// 轮训调用AI
export function callAiPolling(params){
	return request.post("/api/funFunEsthetics/getAIStatus",params)
}

// 发送短信验证码
export function sendSmsCode(params){
	return request.get("/api/funFunEsthetics/send/code", params)
}

// 手机号验证码登录
export function loginWithSms(params){
	return request.post("/api/funFunEsthetics/login", params)
}

// 领取优惠券
export function takeCoupon(params){
	return request.post("/api/funFunEsthetics/user/discount", params)
}
// 是否是人脸
export function invokeDetectData(params){
	return request.post("/api/funFunEsthetics/invokeDetect", params)
}

// 评分
export function assessScore(params){
	return request.post("/api/funFunEsthetics/user/comment", params)
}